import 'dart:async';
import 'dart:convert';
import 'dart:math' show min;
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'auth_logger.dart';
import 'auth_logger_provider.dart';
import 'auth_providers.dart';
import 'auth_repository.dart';

/// Provider for the session manager
final sessionManagerProvider = rp.Provider<SessionManager>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  final authLogger = ref.watch(authLoggerProvider);
  return SessionManager(repository, authLogger);
});

/// Provider for session status
final sessionStatusProvider = rp.StateProvider<SessionStatus>(
  (ref) => SessionStatus.unknown,
);

/// Types of refresh errors for categorization
enum RefreshErrorType {
  network,
  authentication,
  tokenExpired,
  serverError,
  unknown,
}

/// Represents a refresh error with categorization
class RefreshError {
  final RefreshErrorType type;
  final String message;
  final dynamic originalError;
  final DateTime timestamp;
  final int attemptNumber;

  RefreshError({
    required this.type,
    required this.message,
    required this.originalError,
    required this.attemptNumber,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'RefreshError(type: $type, message: $message, attempt: $attemptNumber, time: $timestamp)';
  }
}

/// Types of resume validation failures
enum ResumeValidationFailureType {
  noSession,
  sessionExpired,
  sessionCorrupted,
  extendedInactivity,
  networkIssue,
  persistenceFailure,
}

/// Result of resume session validation
class ResumeValidationResult {
  final bool isValid;
  final ResumeValidationFailureType? failureType;
  final String reason;
  final Session? currentSession;
  final Duration? inactivityDuration;

  ResumeValidationResult.valid(this.currentSession)
    : isValid = true,
      failureType = null,
      reason = 'Session validation passed',
      inactivityDuration = null;

  ResumeValidationResult.invalid({
    required this.failureType,
    required this.reason,
    this.currentSession,
    this.inactivityDuration,
  }) : isValid = false;
}

/// Enum for session status
enum SessionStatus { unknown, valid, expired, refreshing, error }

/// A service responsible for managing authentication sessions
///
/// This service handles session persistence, token refresh, and
/// session expiration to ensure the user remains authenticated.
class SessionManager {
  final AuthRepository _repository;
  final AuthLogger _authLogger;
  Timer? _refreshTimer;
  bool _isRefreshing = false;

  // Keys for storing session data
  static const String _sessionExpiryKey = 'session_expiry_time';
  static const String _sessionRefreshTokenKey = 'session_refresh_token';
  static const String _sessionAccessTokenKey = 'session_access_token';
  static const String _lastRefreshTimeKey = 'last_refresh_time';
  static const String _lastAppActiveTimeKey = 'last_app_active_time';
  static const String _sessionIntegrityHashKey = 'session_integrity_hash';
  static const String _sessionVersionKey = 'session_version';

  // Constants for token refresh
  // Minimum refresh threshold to ensure we never refresh too late
  static const Duration _minRefreshThreshold = Duration(minutes: 5);
  // Maximum refresh threshold to avoid refreshing too early
  static const Duration _maxRefreshThreshold = Duration(minutes: 20);
  // Percentage of token lifetime to use as refresh threshold (25% = refresh when 75% of lifetime remains)
  static const double _refreshThresholdPercentage = 0.25;
  // Previous fixed delay replaced with initial delay for exponential backoff
  static const Duration _initialRetryDelay = Duration(seconds: 2);
  static const int _maxRefreshRetries = 3;
  // Proactive refresh buffer - refresh this much earlier when user is active
  static const Duration _proactiveRefreshBuffer = Duration(minutes: 2);

  // Constants for app state tracking
  static const Duration _extendedInactivityThreshold = Duration(minutes: 30);

  /// Constructor
  SessionManager(this._repository, this._authLogger);

  /// Initialize the session manager
  ///
  /// This method sets up the session refresh timer and checks
  /// the current session status.
  Future<void> initialize() async {
    _authLogger.addLogEntry(
      'Initializing session manager',
      importance: AuthLogImportance.important,
    );

    // Ensure Supabase is initialized
    if (!_repository.isSupabaseInitialized()) {
      _authLogger.addLogEntry(
        'Supabase not initialized, initializing now',
        importance: AuthLogImportance.important,
      );
      await _repository.initialize();
    }

    // Check for session corruption and attempt recovery
    final corruptionCheckPassed = await _checkAndRecoverSessionCorruption();
    if (!corruptionCheckPassed) {
      _authLogger.addLogEntry(
        'Session corruption detected and recovery failed',
        importance: AuthLogImportance.critical,
      );
      // Clear potentially corrupted data
      await _clearPersistedSession();
    }

    // Check if we have a persisted session
    await _loadPersistedSession();

    // Check and refresh the current session
    await _checkAndRefreshSession();

    // Start the refresh timer
    _startRefreshTimer();

    _authLogger.addLogEntry(
      'Session manager initialization complete',
      importance: AuthLogImportance.important,
    );
  }

  /// Load persisted session data and attempt to restore the session if needed
  Future<void> _loadPersistedSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if we have a current session already
      final currentSession = _repository.getCurrentSession();
      if (currentSession != null) {
        _authLogger.addLogEntry(
          'Active session already exists, no need to restore from persistence',
          importance: AuthLogImportance.verbose,
        );

        // Still persist the current session to ensure it's saved
        await _persistSession(currentSession);
        _updateSessionStatus(SessionStatus.valid);
        return;
      }

      // Validate and load persisted session data
      final sessionData = await _validateAndLoadSessionData(prefs);

      if (sessionData == null) {
        _authLogger.addLogEntry(
          'No valid persisted session found',
          importance: AuthLogImportance.verbose,
        );
        _updateSessionStatus(SessionStatus.unknown);
        return;
      }

      final expiryTime = sessionData['expiryTime'] as DateTime;
      final refreshToken = sessionData['refreshToken'] as String?;
      final accessToken = sessionData['accessToken'] as String?;
      final now = DateTime.now();

      // Log the persisted session details
      _authLogger.addLogEntry(
        'Found persisted session data:',
        importance: AuthLogImportance.verbose,
      );
      _authLogger.addLogEntry(
        '  Expiry time: ${expiryTime.toIso8601String()}',
        importance: AuthLogImportance.verbose,
      );
      _authLogger.addLogEntry(
        '  Has refresh token: ${refreshToken != null && refreshToken.isNotEmpty}',
        importance: AuthLogImportance.verbose,
      );
      _authLogger.addLogEntry(
        '  Has access token: ${accessToken != null && accessToken.isNotEmpty}',
        importance: AuthLogImportance.verbose,
      );

      if (expiryTime.isAfter(now)) {
        _authLogger.addLogEntry(
          'Found valid persisted session expiring at ${expiryTime.toIso8601String()}',
          importance: AuthLogImportance.verbose,
        );

        // Check if we have both tokens
        if (refreshToken != null &&
            refreshToken.isNotEmpty &&
            accessToken != null &&
            accessToken.isNotEmpty) {
          _authLogger.addLogEntry(
            'Found valid refresh and access tokens',
            importance: AuthLogImportance.verbose,
          );

          // Force a session refresh to ensure we have a valid session
          _authLogger.addLogEntry(
            'Forcing session refresh to ensure validity',
            importance: AuthLogImportance.important,
          );

          // Update status to refreshing
          _updateSessionStatus(SessionStatus.refreshing);

          // Try to refresh the session
          await _checkAndRefreshSession();
        } else {
          _authLogger.addLogEntry(
            'Missing tokens in persisted session: refreshToken=${refreshToken != null}, accessToken=${accessToken != null}',
            importance: AuthLogImportance.important,
          );

          // Attempt recovery with available tokens
          await _attemptSessionRecovery(refreshToken, accessToken);
        }
      } else {
        _authLogger.addLogEntry(
          'Found expired persisted session',
          importance: AuthLogImportance.important,
        );

        // If we have a refresh token, we can try to refresh
        if (refreshToken != null && refreshToken.isNotEmpty) {
          _authLogger.addLogEntry(
            'Found refresh token, will attempt to refresh session',
            importance: AuthLogImportance.important,
          );

          // Update status to refreshing
          _updateSessionStatus(SessionStatus.refreshing);

          // Try to refresh the session
          await _checkAndRefreshSession();
        } else {
          _authLogger.addLogEntry(
            'No refresh token found, session cannot be restored',
            importance: AuthLogImportance.critical,
          );
          await _handleSessionExpiration(
            reason: 'No refresh token available to restore expired session',
          );
        }
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error loading persisted session: $e',
        importance: AuthLogImportance.critical,
      );

      // Attempt to recover from corrupted session data
      await _recoverFromCorruptedSession(e);
    }
  }

  /// Persist session data with retry logic and comprehensive error handling
  Future<void> _persistSession(Session session) async {
    const maxRetries = 3;
    const baseDelay = Duration(milliseconds: 100);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final prefs = await SharedPreferences.getInstance();

        // Validate session data before persisting
        if (!_validateSessionForPersistence(session)) {
          _authLogger.addLogEntry(
            'Session validation failed, cannot persist invalid session',
            importance: AuthLogImportance.critical,
          );
          return;
        }

        // Store expiry time with validation
        if (session.expiresAt != null) {
          final expiryTimeMs = session.expiresAt! * 1000;

          // Validate expiry time is reasonable
          final expiryTime = DateTime.fromMillisecondsSinceEpoch(expiryTimeMs);
          final now = DateTime.now();
          if (expiryTime.isBefore(now.subtract(const Duration(minutes: 5))) ||
              expiryTime.isAfter(now.add(const Duration(days: 30)))) {
            _authLogger.addLogEntry(
              'Invalid session expiry time: ${expiryTime.toIso8601String()}',
              importance: AuthLogImportance.critical,
            );
            return;
          }

          await prefs.setInt(_sessionExpiryKey, expiryTimeMs);
          _authLogger.addLogEntry(
            'Persisted session expiry time: ${expiryTime.toIso8601String()}',
            importance: AuthLogImportance.verbose,
          );
        }

        // Store refresh token with validation
        if (session.refreshToken != null && session.refreshToken!.isNotEmpty) {
          if (!_isValidToken(session.refreshToken!)) {
            _authLogger.addLogEntry(
              'Invalid refresh token format, skipping persistence',
              importance: AuthLogImportance.important,
            );
          } else {
            await prefs.setString(
              _sessionRefreshTokenKey,
              session.refreshToken!,
            );
            _authLogger.addLogEntry(
              'Persisted refresh token',
              importance: AuthLogImportance.verbose,
            );
          }
        }

        // Store access token with validation
        if (session.accessToken.isNotEmpty &&
            _isValidToken(session.accessToken)) {
          await prefs.setString(_sessionAccessTokenKey, session.accessToken);
          _authLogger.addLogEntry(
            'Persisted access token',
            importance: AuthLogImportance.verbose,
          );
        } else {
          _authLogger.addLogEntry(
            'Invalid access token format, cannot persist session',
            importance: AuthLogImportance.critical,
          );
          return;
        }

        // Store session integrity hash
        await _storeSessionIntegrityHash(session, prefs);

        // Verify persistence by reading back the data
        if (await _verifyPersistedSession(session)) {
          // Update the last refresh time
          await _updateLastRefreshTime();

          _authLogger.addLogEntry(
            'Session persistence completed successfully',
            importance: AuthLogImportance.verbose,
          );
          return; // Success, exit retry loop
        } else {
          throw Exception('Session persistence verification failed');
        }
      } catch (e) {
        _authLogger.addLogEntry(
          'Error persisting session (attempt $attempt/$maxRetries): $e',
          importance: attempt == maxRetries
              ? AuthLogImportance.critical
              : AuthLogImportance.important,
        );

        if (attempt == maxRetries) {
          // Final attempt failed, log critical error
          _authLogger.addLogEntry(
            'CRITICAL: Failed to persist session after $maxRetries attempts',
            importance: AuthLogImportance.critical,
          );

          // Try to clear potentially corrupted data
          try {
            await _clearPersistedSession();
          } catch (clearError) {
            _authLogger.addLogEntry(
              'Failed to clear corrupted session data: $clearError',
              importance: AuthLogImportance.critical,
            );
          }
          return;
        }

        // Wait before retrying with exponential backoff
        final delay = Duration(
          milliseconds: baseDelay.inMilliseconds * (1 << (attempt - 1)),
        );
        await Future.delayed(delay);
      }
    }
  }

  /// Clear persisted session data with retry logic
  Future<void> _clearPersistedSession() async {
    const maxRetries = 3;
    const baseDelay = Duration(milliseconds: 50);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final prefs = await SharedPreferences.getInstance();

        // Remove all session-related keys
        final futures = <Future<bool>>[
          prefs.remove(_sessionExpiryKey),
          prefs.remove(_sessionRefreshTokenKey),
          prefs.remove(_sessionAccessTokenKey),
          prefs.remove(_sessionIntegrityHashKey),
          prefs.remove(_sessionVersionKey),
        ];

        // Wait for all removals to complete
        final results = await Future.wait(futures);

        // Verify all removals were successful
        bool allRemoved = results.every((result) => result);
        if (!allRemoved) {
          throw Exception('Some session data could not be removed');
        }

        // Verify the data was actually cleared
        final verificationResults = [
          prefs.get(_sessionExpiryKey),
          prefs.get(_sessionRefreshTokenKey),
          prefs.get(_sessionAccessTokenKey),
          prefs.get(_sessionIntegrityHashKey),
          prefs.get(_sessionVersionKey),
        ];

        final anyDataRemaining = verificationResults.any(
          (result) => result != null,
        );

        if (anyDataRemaining) {
          throw Exception(
            'Session data verification failed - some data still present',
          );
        }

        _authLogger.addLogEntry(
          'Cleared persisted session data successfully',
          importance: AuthLogImportance.important,
        );
        return; // Success, exit retry loop
      } catch (e) {
        _authLogger.addLogEntry(
          'Error clearing persisted session (attempt $attempt/$maxRetries): $e',
          importance: attempt == maxRetries
              ? AuthLogImportance.critical
              : AuthLogImportance.important,
        );

        if (attempt == maxRetries) {
          _authLogger.addLogEntry(
            'CRITICAL: Failed to clear persisted session after $maxRetries attempts',
            importance: AuthLogImportance.critical,
          );
          return;
        }

        // Wait before retrying with exponential backoff
        final delay = Duration(
          milliseconds: baseDelay.inMilliseconds * (1 << (attempt - 1)),
        );
        await Future.delayed(delay);
      }
    }
  }

  /// Validate and load session data from SharedPreferences
  /// Returns null if no valid session data is found
  Future<Map<String, dynamic>?> _validateAndLoadSessionData(
    SharedPreferences prefs,
  ) async {
    try {
      final expiryTimeMs = prefs.getInt(_sessionExpiryKey);
      final refreshToken = prefs.getString(_sessionRefreshTokenKey);
      final accessToken = prefs.getString(_sessionAccessTokenKey);

      // Check if we have at least expiry time
      if (expiryTimeMs == null) {
        _authLogger.addLogEntry(
          'No session expiry time found in storage',
          importance: AuthLogImportance.verbose,
        );
        return null;
      }

      // Validate expiry time is reasonable (not too far in past or future)
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(expiryTimeMs);
      final now = DateTime.now();
      final maxPastTime = now.subtract(const Duration(days: 30)); // 30 days ago
      final maxFutureTime = now.add(
        const Duration(days: 30),
      ); // 30 days from now

      if (expiryTime.isBefore(maxPastTime) ||
          expiryTime.isAfter(maxFutureTime)) {
        _authLogger.addLogEntry(
          'Session expiry time is unreasonable: ${expiryTime.toIso8601String()}',
          importance: AuthLogImportance.important,
        );
        await _clearPersistedSession(); // Clear corrupted data
        return null;
      }

      // Validate tokens if present
      if (refreshToken != null && !_isValidToken(refreshToken)) {
        _authLogger.addLogEntry(
          'Invalid refresh token format detected',
          importance: AuthLogImportance.important,
        );
        await _clearPersistedSession(); // Clear corrupted data
        return null;
      }

      if (accessToken != null && !_isValidToken(accessToken)) {
        _authLogger.addLogEntry(
          'Invalid access token format detected',
          importance: AuthLogImportance.important,
        );
        await _clearPersistedSession(); // Clear corrupted data
        return null;
      }

      return {
        'expiryTime': expiryTime,
        'refreshToken': refreshToken,
        'accessToken': accessToken,
      };
    } catch (e) {
      _authLogger.addLogEntry(
        'Error validating session data: $e',
        importance: AuthLogImportance.critical,
      );
      await _clearPersistedSession(); // Clear potentially corrupted data
      return null;
    }
  }

  /// Basic token validation - checks if token looks like a valid JWT or similar
  bool _isValidToken(String token) {
    if (token.isEmpty) return false;

    // Basic JWT format check (should have 3 parts separated by dots)
    final parts = token.split('.');
    if (parts.length == 3) {
      // Check each part is base64-like (contains valid base64 characters)
      for (final part in parts) {
        if (part.isEmpty || !RegExp(r'^[A-Za-z0-9_-]+$').hasMatch(part)) {
          return false;
        }
      }
      return true;
    }

    // For non-JWT tokens, just check it's not empty and has reasonable length
    return token.length > 10 && token.length < 10000;
  }

  /// Validate session data before persistence
  bool _validateSessionForPersistence(Session session) {
    try {
      // Check if session has required fields
      if (session.accessToken.isEmpty) {
        _authLogger.addLogEntry(
          'Session validation failed: empty access token',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      // Validate access token format
      if (!_isValidToken(session.accessToken)) {
        _authLogger.addLogEntry(
          'Session validation failed: invalid access token format',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      // Check expiry time if present
      if (session.expiresAt != null) {
        final expiryTime = DateTime.fromMillisecondsSinceEpoch(
          session.expiresAt! * 1000,
        );
        final now = DateTime.now();

        // Session should not be expired by more than 5 minutes (grace period)
        if (expiryTime.isBefore(now.subtract(const Duration(minutes: 5)))) {
          _authLogger.addLogEntry(
            'Session validation failed: session is too far expired',
            importance: AuthLogImportance.important,
          );
          return false;
        }

        // Session should not expire too far in the future (sanity check)
        if (expiryTime.isAfter(now.add(const Duration(days: 30)))) {
          _authLogger.addLogEntry(
            'Session validation failed: expiry time too far in future',
            importance: AuthLogImportance.important,
          );
          return false;
        }
      }

      // Validate refresh token if present
      if (session.refreshToken != null &&
          session.refreshToken!.isNotEmpty &&
          !_isValidToken(session.refreshToken!)) {
        _authLogger.addLogEntry(
          'Session validation failed: invalid refresh token format',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      // Check if user exists
      if (session.user.id.isEmpty) {
        _authLogger.addLogEntry(
          'Session validation failed: empty user ID',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      return true;
    } catch (e) {
      _authLogger.addLogEntry(
        'Session validation error: $e',
        importance: AuthLogImportance.important,
      );
      return false;
    }
  }

  /// Verify that session data was persisted correctly
  Future<bool> _verifyPersistedSession(Session originalSession) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if access token was stored correctly
      final storedAccessToken = prefs.getString(_sessionAccessTokenKey);
      if (storedAccessToken != originalSession.accessToken) {
        _authLogger.addLogEntry(
          'Session verification failed: access token mismatch',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      // Check if expiry time was stored correctly
      if (originalSession.expiresAt != null) {
        final storedExpiryMs = prefs.getInt(_sessionExpiryKey);
        final expectedExpiryMs = originalSession.expiresAt! * 1000;
        if (storedExpiryMs != expectedExpiryMs) {
          _authLogger.addLogEntry(
            'Session verification failed: expiry time mismatch',
            importance: AuthLogImportance.important,
          );
          return false;
        }
      }

      // Check if refresh token was stored correctly (if present)
      if (originalSession.refreshToken != null &&
          originalSession.refreshToken!.isNotEmpty) {
        final storedRefreshToken = prefs.getString(_sessionRefreshTokenKey);
        if (storedRefreshToken != originalSession.refreshToken) {
          _authLogger.addLogEntry(
            'Session verification failed: refresh token mismatch',
            importance: AuthLogImportance.important,
          );
          return false;
        }
      }

      // Verify session integrity
      if (!await _verifySessionIntegrity(originalSession, prefs)) {
        _authLogger.addLogEntry(
          'Session verification failed: integrity check failed',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      return true;
    } catch (e) {
      _authLogger.addLogEntry(
        'Session verification error: $e',
        importance: AuthLogImportance.important,
      );
      return false;
    }
  }

  /// Generate a simple integrity hash for session data
  String _generateSessionIntegrityHash(Session session) {
    // Create a simple hash based on session data
    final data = {
      'accessToken': session.accessToken,
      'refreshToken': session.refreshToken ?? '',
      'expiresAt': session.expiresAt?.toString() ?? '',
      'userId': session.user.id,
      'userEmail': session.user.email ?? '',
    };

    // Convert to JSON string and generate a simple hash
    final jsonString = jsonEncode(data);
    return jsonString.hashCode.toString();
  }

  /// Verify session integrity using stored hash
  Future<bool> _verifySessionIntegrity(
    Session session,
    SharedPreferences prefs,
  ) async {
    try {
      final storedHash = prefs.getString(_sessionIntegrityHashKey);
      final currentHash = _generateSessionIntegrityHash(session);

      if (storedHash == null) {
        // No stored hash, this might be an old session, generate and store one
        await prefs.setString(_sessionIntegrityHashKey, currentHash);
        _authLogger.addLogEntry(
          'No integrity hash found, generated new one',
          importance: AuthLogImportance.verbose,
        );
        return true;
      }

      if (storedHash != currentHash) {
        _authLogger.addLogEntry(
          'Session integrity hash mismatch - possible corruption',
          importance: AuthLogImportance.critical,
        );
        return false;
      }

      return true;
    } catch (e) {
      _authLogger.addLogEntry(
        'Error verifying session integrity: $e',
        importance: AuthLogImportance.important,
      );
      return false;
    }
  }

  /// Store session integrity hash
  Future<void> _storeSessionIntegrityHash(
    Session session,
    SharedPreferences prefs,
  ) async {
    try {
      final hash = _generateSessionIntegrityHash(session);
      await prefs.setString(_sessionIntegrityHashKey, hash);

      // Also store session version for future compatibility
      await prefs.setInt(_sessionVersionKey, 1);

      _authLogger.addLogEntry(
        'Stored session integrity hash',
        importance: AuthLogImportance.verbose,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Error storing session integrity hash: $e',
        importance: AuthLogImportance.important,
      );
    }
  }

  /// Check for session corruption and attempt recovery
  Future<bool> _checkAndRecoverSessionCorruption() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentSession = _repository.getCurrentSession();

      if (currentSession == null) {
        // No current session, check if we have orphaned persisted data
        final hasPersistedData =
            prefs.containsKey(_sessionAccessTokenKey) ||
            prefs.containsKey(_sessionRefreshTokenKey) ||
            prefs.containsKey(_sessionExpiryKey);

        if (hasPersistedData) {
          _authLogger.addLogEntry(
            'Found orphaned session data without active session',
            importance: AuthLogImportance.important,
          );
          await _clearPersistedSession();
          return true; // Recovered by cleaning up
        }

        return true; // No corruption, no session
      }

      // Verify current session integrity
      if (!await _verifySessionIntegrity(currentSession, prefs)) {
        _authLogger.addLogEntry(
          'Session corruption detected, attempting recovery',
          importance: AuthLogImportance.critical,
        );

        // Try to recover by re-persisting the current session
        await _persistSession(currentSession);

        // Verify the recovery worked
        if (await _verifySessionIntegrity(currentSession, prefs)) {
          _authLogger.addLogEntry(
            'Session corruption recovered successfully',
            importance: AuthLogImportance.important,
          );
          return true;
        } else {
          _authLogger.addLogEntry(
            'Failed to recover from session corruption',
            importance: AuthLogImportance.critical,
          );
          return false;
        }
      }

      return true; // No corruption detected
    } catch (e) {
      _authLogger.addLogEntry(
        'Error checking session corruption: $e',
        importance: AuthLogImportance.critical,
      );
      return false;
    }
  }

  // Track app state for adaptive refresh
  bool _isAppActive = true;
  DateTime? _backgroundStartTime;
  int _backgroundRefreshCount = 0;

  /// Attempt to recover session with available tokens
  Future<void> _attemptSessionRecovery(
    String? refreshToken,
    String? accessToken,
  ) async {
    _authLogger.addLogEntry(
      'Attempting session recovery with available tokens',
      importance: AuthLogImportance.important,
    );

    // If we have a refresh token, try to use it
    if (refreshToken != null && refreshToken.isNotEmpty) {
      _authLogger.addLogEntry(
        'Attempting recovery with refresh token',
        importance: AuthLogImportance.important,
      );
      _updateSessionStatus(SessionStatus.refreshing);
      await _checkAndRefreshSession();
    } else if (accessToken != null && accessToken.isNotEmpty) {
      // If we only have access token, check if it's still valid by trying to use it
      _authLogger.addLogEntry(
        'Attempting recovery with access token only',
        importance: AuthLogImportance.important,
      );

      // Try to validate the access token by making a simple request
      final isValid = await _validateAccessToken(accessToken);
      if (isValid) {
        _authLogger.addLogEntry(
          'Access token is still valid, session recovered',
          importance: AuthLogImportance.important,
        );
        _updateSessionStatus(SessionStatus.valid);
      } else {
        _authLogger.addLogEntry(
          'Access token is invalid, cannot recover session',
          importance: AuthLogImportance.important,
        );
        await _handleSessionExpiration(
          reason: 'Invalid access token during recovery',
        );
      }
    } else {
      _authLogger.addLogEntry(
        'No valid tokens available for recovery',
        importance: AuthLogImportance.important,
      );
      await _handleSessionExpiration(
        reason: 'No tokens available for recovery',
      );
    }
  }

  /// Validate access token by attempting to use it
  Future<bool> _validateAccessToken(String accessToken) async {
    try {
      // Try to get current user with the access token
      // This is a simple way to validate if the token is still valid
      final currentSession = _repository.getCurrentSession();
      if (currentSession != null && currentSession.accessToken == accessToken) {
        return true; // Token matches current session
      }

      // If no current session or token doesn't match, consider it invalid
      return false;
    } catch (e) {
      _authLogger.addLogEntry(
        'Error validating access token: $e',
        importance: AuthLogImportance.important,
      );
      return false;
    }
  }

  /// Recover from corrupted session data
  Future<void> _recoverFromCorruptedSession(dynamic error) async {
    _authLogger.addLogEntry(
      'Attempting to recover from corrupted session data',
      importance: AuthLogImportance.critical,
    );
    _authLogger.addLogEntry(
      'Corruption error: $error',
      importance: AuthLogImportance.critical,
    );

    try {
      // Clear all potentially corrupted session data
      await _clearPersistedSession();

      // Check if there's still a valid current session in memory
      final currentSession = _repository.getCurrentSession();
      if (currentSession != null) {
        _authLogger.addLogEntry(
          'Found valid session in memory, re-persisting it',
          importance: AuthLogImportance.important,
        );

        // Re-persist the current session
        await _persistSession(currentSession);
        _updateSessionStatus(SessionStatus.valid);
      } else {
        _authLogger.addLogEntry(
          'No valid session in memory, user needs to re-authenticate',
          importance: AuthLogImportance.important,
        );
        _updateSessionStatus(SessionStatus.unknown);
      }
    } catch (recoveryError) {
      _authLogger.addLogEntry(
        'Failed to recover from corrupted session: $recoveryError',
        importance: AuthLogImportance.critical,
      );
      _updateSessionStatus(SessionStatus.error);
    }
  }

  /// Calculate dynamic refresh threshold based on token lifetime
  Duration _calculateRefreshThreshold(Session session) {
    if (session.expiresAt == null) {
      // If no expiry time, use minimum threshold
      return _minRefreshThreshold;
    }

    // Calculate token lifetime
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();
    final tokenLifetime = expiresAt.difference(now);

    // Calculate threshold as percentage of remaining lifetime
    final calculatedThreshold = Duration(
      milliseconds: (tokenLifetime.inMilliseconds * _refreshThresholdPercentage)
          .round(),
    );

    // Apply proactive buffer if user is active
    final proactiveThreshold = _isAppActive
        ? calculatedThreshold + _proactiveRefreshBuffer
        : calculatedThreshold;

    // Clamp between min and max thresholds
    if (proactiveThreshold < _minRefreshThreshold) {
      return _minRefreshThreshold;
    } else if (proactiveThreshold > _maxRefreshThreshold) {
      return _maxRefreshThreshold;
    } else {
      return proactiveThreshold;
    }
  }

  /// Calculate smart refresh interval based on token expiry
  Duration _calculateSmartRefreshInterval(Session session) {
    if (session.expiresAt == null) {
      // If no expiry time, use a default interval
      return const Duration(minutes: 15);
    }

    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();
    final timeUntilExpiry = expiresAt.difference(now);
    final refreshThreshold = _calculateRefreshThreshold(session);

    // Calculate when we need to refresh
    final timeUntilRefresh = timeUntilExpiry - refreshThreshold;

    if (timeUntilRefresh <= Duration.zero) {
      // Need to refresh immediately
      return Duration.zero;
    }

    // Set interval to check halfway to refresh time, but not too frequently
    final halfwayInterval = Duration(
      milliseconds: timeUntilRefresh.inMilliseconds ~/ 2,
    );

    // Minimum interval to avoid too frequent checks
    const minInterval = Duration(minutes: 2);
    // Maximum interval to ensure we don't miss refresh opportunities
    const maxInterval = Duration(minutes: 15);

    if (halfwayInterval < minInterval) {
      return minInterval;
    } else if (halfwayInterval > maxInterval) {
      return maxInterval;
    } else {
      return halfwayInterval;
    }
  }

  /// Get the appropriate refresh interval based on current session and app state
  Duration _getAdaptiveRefreshInterval() {
    final currentSession = _repository.getCurrentSession();

    if (currentSession != null) {
      // Use smart interval based on actual token expiry
      final smartInterval = _calculateSmartRefreshInterval(currentSession);

      // Adjust for app state - longer intervals when in background
      if (_isAppActive) {
        return smartInterval;
      } else {
        // Increase interval by 50% when in background to save battery
        return Duration(
          milliseconds: (smartInterval.inMilliseconds * 1.5).round(),
        );
      }
    } else {
      // No session, use default interval
      return const Duration(minutes: 10);
    }
  }

  /// Start the refresh timer
  ///
  /// This method starts a timer that periodically checks and
  /// refreshes the session token if needed, using an adaptive interval
  void _startRefreshTimer() {
    _refreshTimer?.cancel();

    // Get adaptive interval based on current session and app state
    final adaptiveInterval = _getAdaptiveRefreshInterval();

    _refreshTimer = Timer.periodic(adaptiveInterval, (_) async {
      await _checkAndRefreshSession();

      // After refresh, check if we need to adjust the timer interval
      final newInterval = _getAdaptiveRefreshInterval();
      if (newInterval != adaptiveInterval) {
        _authLogger.addLogEntry(
          'Timer interval changed from ${adaptiveInterval.inMinutes} to ${newInterval.inMinutes} minutes, restarting timer',
          importance: AuthLogImportance.verbose,
        );
        _startRefreshTimer(); // Restart with new interval
      }
    });

    _authLogger.addLogEntry(
      'Session refresh timer started (adaptive interval: ${adaptiveInterval.inMinutes} minutes, '
      'app active: $_isAppActive)',
      importance: AuthLogImportance.verbose,
    );
  }

  /// Restart refresh timer with updated interval
  /// This should be called when session state changes significantly
  void _restartRefreshTimer() {
    final currentInterval = _refreshTimer?.isActive == true
        ? _getAdaptiveRefreshInterval()
        : null;

    final newInterval = _getAdaptiveRefreshInterval();

    // Only restart if interval has changed significantly (more than 1 minute difference)
    if (currentInterval == null ||
        (currentInterval - newInterval).abs() > const Duration(minutes: 1)) {
      _authLogger.addLogEntry(
        'Restarting refresh timer due to significant interval change',
        importance: AuthLogImportance.verbose,
      );
      _startRefreshTimer();
    }
  }

  /// Check if we should defer refresh due to network conditions and app state
  bool _shouldDeferRefresh(Session session) {
    // Calculate how critical the refresh is
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();
    final timeUntilExpiry = expiresAt.difference(now);

    // If token expires in less than 2 minutes, don't defer regardless of conditions
    if (timeUntilExpiry < const Duration(minutes: 2)) {
      _authLogger.addLogEntry(
        'Token expires very soon, not deferring refresh despite conditions',
        importance: AuthLogImportance.important,
      );
      return false;
    }

    // Enhanced background deferral logic
    if (!_isAppActive) {
      return _shouldDeferBackgroundRefresh(session, timeUntilExpiry);
    }

    // Don't defer if user is actively using the app
    return false;
  }

  /// Determine if background refresh should be deferred
  bool _shouldDeferBackgroundRefresh(
    Session session,
    Duration timeUntilExpiry,
  ) {
    // Calculate background-specific thresholds
    final backgroundRefreshThreshold = _calculateBackgroundRefreshThreshold(
      session,
    );

    // If we're within the background refresh threshold, don't defer
    if (timeUntilExpiry <= backgroundRefreshThreshold) {
      _authLogger.addLogEntry(
        'Background refresh needed - within threshold (${timeUntilExpiry.inMinutes} <= ${backgroundRefreshThreshold.inMinutes} minutes)',
        importance: AuthLogImportance.verbose,
      );
      return false;
    }

    // Check how long the app has been in background
    final backgroundDuration = _getBackgroundDuration();

    // If app has been in background for a long time, be more conservative
    if (backgroundDuration != null &&
        backgroundDuration > const Duration(hours: 1)) {
      // For long background periods, only defer if we have substantial time left
      if (timeUntilExpiry > const Duration(hours: 2)) {
        _authLogger.addLogEntry(
          'Long background period detected, deferring refresh (${timeUntilExpiry.inHours} hours remaining)',
          importance: AuthLogImportance.verbose,
        );
        return true;
      }
    } else {
      // For shorter background periods, defer if we have reasonable time
      if (timeUntilExpiry > const Duration(minutes: 45)) {
        _authLogger.addLogEntry(
          'App in background with sufficient token time, deferring refresh (${timeUntilExpiry.inMinutes} minutes remaining)',
          importance: AuthLogImportance.verbose,
        );
        return true;
      }
    }

    return false;
  }

  /// Get duration the app has been in background
  Duration? _getBackgroundDuration() {
    if (_isAppActive || _backgroundStartTime == null) return null;

    return DateTime.now().difference(_backgroundStartTime!);
  }

  /// Track background refresh attempts
  void _incrementBackgroundRefreshCount() {
    if (!_isAppActive) {
      _backgroundRefreshCount++;
      _authLogger.addLogEntry(
        'Background refresh count: $_backgroundRefreshCount',
        importance: AuthLogImportance.verbose,
      );
    }
  }

  /// Reset background tracking when app becomes active
  void _resetBackgroundTracking() {
    if (_backgroundStartTime != null) {
      final backgroundDuration = DateTime.now().difference(
        _backgroundStartTime!,
      );
      _authLogger.addLogEntry(
        'App was in background for ${backgroundDuration.inMinutes} minutes with $_backgroundRefreshCount refresh attempts',
        importance: AuthLogImportance.verbose,
      );
    }

    _backgroundStartTime = null;
    _backgroundRefreshCount = 0;
  }

  /// Enhanced refresh check with network awareness
  Future<bool> _shouldRefreshNow(Session session) async {
    final dynamicRefreshThreshold = _calculateRefreshThreshold(session);
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();
    final timeUntilExpiry = expiresAt.difference(now);

    // Check if refresh is needed based on time
    final refreshNeeded = timeUntilExpiry < dynamicRefreshThreshold;

    if (!refreshNeeded) {
      return false;
    }

    // Check if we should defer due to network/app state
    if (_shouldDeferRefresh(session)) {
      _authLogger.addLogEntry(
        'Refresh needed but deferred due to network/app conditions',
        importance: AuthLogImportance.verbose,
      );
      return false;
    }

    return true;
  }

  /// Store the last refresh time
  Future<void> _updateLastRefreshTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_lastRefreshTimeKey, now);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error storing last refresh time: $e',
        importance: AuthLogImportance.important,
      );
    }
  }

  /// Store the last time the app was active
  Future<void> _updateLastActiveTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_lastAppActiveTimeKey, now);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error storing last active time: $e',
        importance: AuthLogImportance.important,
      );
    }
  }

  /// Update the session status
  void _updateSessionStatus(SessionStatus status) {
    if (_repository.isSupabaseInitialized()) {
      try {
        // Use the global provider container to update the session status
        rp.ProviderContainer().read(sessionStatusProvider.notifier).state =
            status;

        // Log with appropriate importance based on status
        final importance =
            status == SessionStatus.error || status == SessionStatus.expired
            ? AuthLogImportance
                  .critical // Critical for error or expired
            : status == SessionStatus.valid
            ? AuthLogImportance
                  .verbose // Verbose for valid (common case)
            : AuthLogImportance
                  .important; // Important for others (refreshing, unknown)

        _authLogger.addLogEntry(
          'Session status updated to: $status',
          importance: importance,
        );
      } catch (e) {
        // Log error but don't crash the app
        _authLogger.addLogEntry(
          'Error updating session status: $e',
          importance: AuthLogImportance.critical,
        );
      }
    }
  }

  /// Check and refresh the session if needed
  ///
  /// This method checks if the current session token is about to expire
  /// and refreshes it if necessary.
  Future<void> _checkAndRefreshSession() async {
    // Prevent multiple simultaneous refresh attempts
    if (_isRefreshing) {
      _authLogger.addLogEntry(
        'Session refresh already in progress, skipping',
        importance: AuthLogImportance.verbose,
      );
      return;
    }

    _isRefreshing = true;

    // Track background refresh attempts
    _incrementBackgroundRefreshCount();

    try {
      final currentSession = _repository.getCurrentSession();
      if (currentSession == null) {
        _authLogger.addLogEntry(
          'No active session to refresh',
          importance: AuthLogImportance.important,
        );
        await _handleSessionExpiration(
          reason: 'No active session available for refresh',
        );
        _isRefreshing = false;
        return;
      }

      // Log current session details
      _authLogger.logSessionDetails(currentSession);

      // Check if token is about to expire using dynamic threshold
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        currentSession.expiresAt! * 1000,
      );
      final now = DateTime.now();
      final timeUntilExpiry = expiresAt.difference(now);
      final dynamicRefreshThreshold = _calculateRefreshThreshold(
        currentSession,
      );

      _authLogger.addLogEntry(
        'Dynamic refresh threshold: ${dynamicRefreshThreshold.inMinutes} minutes '
        '(${(dynamicRefreshThreshold.inMilliseconds / timeUntilExpiry.inMilliseconds * 100).toStringAsFixed(1)}% of remaining time)',
        importance: AuthLogImportance.verbose,
      );

      // Use enhanced refresh check with network awareness
      if (await _shouldRefreshNow(currentSession)) {
        _authLogger.addLogEntry(
          'Token expires soon (${timeUntilExpiry.inMinutes} minutes), refreshing session',
          importance: AuthLogImportance.important,
        );
        _authLogger.logSessionRefreshAttempt();

        // Update status to refreshing
        _updateSessionStatus(SessionStatus.refreshing);

        // Try to refresh with retries
        final Session? refreshedSession = await _refreshSessionWithRetries();

        if (refreshedSession != null) {
          _authLogger.logSessionRefreshResult(refreshedSession);

          // Persist the refreshed session
          await _persistSession(refreshedSession);

          // Update status to valid
          _updateSessionStatus(SessionStatus.valid);

          // Restart timer with new session's expiry time
          _restartRefreshTimer();
        } else {
          _authLogger.logSessionRefreshResult(
            null,
            error: 'Refresh failed after retries',
          );

          // Update status to error
          _updateSessionStatus(SessionStatus.error);
        }
      } else {
        _authLogger.addLogEntry(
          'Token still valid for ${timeUntilExpiry.inMinutes} minutes, no refresh needed',
          importance: AuthLogImportance.verbose,
        );

        // Update status to valid
        _updateSessionStatus(SessionStatus.valid);

        // Persist the current session if not already done
        await _persistSession(currentSession);

        // Update the last check time even if no refresh was needed
        await _updateLastRefreshTime();
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error checking/refreshing session: $e',
        importance: AuthLogImportance.critical,
      );
      _authLogger.logSessionRefreshResult(null, error: e.toString());

      // Update status to error
      _updateSessionStatus(SessionStatus.error);
    } finally {
      _isRefreshing = false;
    }
  }

  /// Refresh session with retries using exponential backoff
  ///
  /// This method attempts to refresh the session token with exponential
  /// backoff for retries, providing more resilient error handling.
  Future<Session?> _refreshSessionWithRetries() async {
    return await _refreshSessionWithEnhancedErrorHandling();
  }

  /// Categorize refresh error based on error type and message
  RefreshErrorType _categorizeRefreshError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Network-related errors
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('unreachable') ||
        errorString.contains('socket')) {
      return RefreshErrorType.network;
    }

    // Authentication-related errors
    if (errorString.contains('unauthorized') ||
        errorString.contains('invalid_grant') ||
        errorString.contains('invalid_token') ||
        errorString.contains('refresh_token') ||
        errorString.contains('401')) {
      return RefreshErrorType.authentication;
    }

    // Token expiration errors
    if (errorString.contains('expired') ||
        errorString.contains('token_expired')) {
      return RefreshErrorType.tokenExpired;
    }

    // Server errors
    if (errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504') ||
        errorString.contains('server')) {
      return RefreshErrorType.serverError;
    }

    return RefreshErrorType.unknown;
  }

  /// Determine if we should retry based on error type and current state
  bool _shouldRetryBasedOnError(
    RefreshErrorType errorType,
    int currentRetryCount,
    Session? currentSession,
  ) {
    // Always respect max retry limit
    if (currentRetryCount >= _maxRefreshRetries - 1) {
      return false;
    }

    switch (errorType) {
      case RefreshErrorType.network:
        // Retry network errors - they might be temporary
        return true;

      case RefreshErrorType.serverError:
        // Retry server errors - they might be temporary
        return true;

      case RefreshErrorType.authentication:
        // Don't retry auth errors - they indicate invalid credentials
        // Unless it's the first attempt (might be a temporary auth service issue)
        return currentRetryCount == 0;

      case RefreshErrorType.tokenExpired:
        // Don't retry token expired errors - the refresh token itself is expired
        return false;

      case RefreshErrorType.unknown:
        // Retry unknown errors, but be more conservative
        return currentRetryCount < 2;
    }
  }

  /// Log detailed analysis of refresh failures
  void _logRefreshFailureAnalysis(
    List<RefreshError> errors,
    bool networkIssue,
    bool authIssue,
  ) {
    _authLogger.addLogEntry(
      'Session refresh failure analysis:',
      importance: AuthLogImportance.critical,
    );

    _authLogger.addLogEntry(
      '- Total attempts: ${errors.length}',
      importance: AuthLogImportance.critical,
    );

    _authLogger.addLogEntry(
      '- Network issues detected: $networkIssue',
      importance: AuthLogImportance.critical,
    );

    _authLogger.addLogEntry(
      '- Authentication issues detected: $authIssue',
      importance: AuthLogImportance.critical,
    );

    // Log error breakdown by type
    final errorsByType = <RefreshErrorType, int>{};
    for (final error in errors) {
      errorsByType[error.type] = (errorsByType[error.type] ?? 0) + 1;
    }

    _authLogger.addLogEntry(
      '- Error breakdown: ${errorsByType.entries.map((e) => '${e.key.name}: ${e.value}').join(', ')}',
      importance: AuthLogImportance.critical,
    );

    // Log individual errors
    for (int i = 0; i < errors.length; i++) {
      final error = errors[i];
      _authLogger.addLogEntry(
        '- Attempt ${error.attemptNumber}: ${error.type.name} - ${error.message}',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Get the primary error type from a list of refresh errors
  RefreshErrorType _getPrimaryErrorType(List<RefreshError> errors) {
    if (errors.isEmpty) return RefreshErrorType.unknown;

    // Count occurrences of each error type
    final errorCounts = <RefreshErrorType, int>{};
    for (final error in errors) {
      errorCounts[error.type] = (errorCounts[error.type] ?? 0) + 1;
    }

    // Return the most frequent error type
    return errorCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// Build a descriptive failure reason based on errors
  String _buildFailureReason(
    List<RefreshError> errors,
    RefreshErrorType primaryType,
  ) {
    final baseReason = 'All refresh attempts failed';

    if (errors.isEmpty) {
      return '$baseReason (no error details available)';
    }

    switch (primaryType) {
      case RefreshErrorType.network:
        return '$baseReason due to network connectivity issues';
      case RefreshErrorType.authentication:
        return '$baseReason due to authentication/authorization errors';
      case RefreshErrorType.tokenExpired:
        return '$baseReason due to expired refresh token';
      case RefreshErrorType.serverError:
        return '$baseReason due to server errors';
      case RefreshErrorType.unknown:
        return '$baseReason due to unknown errors';
    }
  }

  /// Calculate adaptive retry delay based on error patterns and attempt number
  Duration _calculateAdaptiveRetryDelay(
    Duration currentDelay,
    int retryCount,
    List<RefreshError> errors,
    bool networkIssueDetected,
    bool authIssueDetected,
  ) {
    // Base exponential backoff: double the delay each time
    Duration baseDelay = Duration(seconds: min(currentDelay.inSeconds * 2, 60));

    // Adjust delay based on error patterns
    if (networkIssueDetected) {
      // For network issues, use longer delays to allow network recovery
      baseDelay = Duration(seconds: min(baseDelay.inSeconds + 5, 120));

      _authLogger.addLogEntry(
        'Network issues detected, extending retry delay to ${baseDelay.inSeconds}s',
        importance: AuthLogImportance.verbose,
      );
    }

    if (authIssueDetected) {
      // For auth issues, use shorter delays since they're likely permanent
      baseDelay = Duration(seconds: min(baseDelay.inSeconds ~/ 2, 30));

      _authLogger.addLogEntry(
        'Auth issues detected, reducing retry delay to ${baseDelay.inSeconds}s',
        importance: AuthLogImportance.verbose,
      );
    }

    // Add jitter to prevent thundering herd
    final jitterMs =
        (baseDelay.inMilliseconds * 0.1 * (0.5 + (retryCount % 10) / 10))
            .round();
    final finalDelay = Duration(
      milliseconds: baseDelay.inMilliseconds + jitterMs,
    );

    // Ensure minimum delay of 1 second
    return finalDelay.inSeconds < 1 ? const Duration(seconds: 1) : finalDelay;
  }

  /// Update retry delay for next iteration based on error patterns
  Duration _updateRetryDelay(Duration currentDelay, List<RefreshError> errors) {
    // If we have recent errors, analyze them for patterns
    if (errors.isNotEmpty) {
      final recentError = errors.last;

      // For certain error types, adjust the progression
      switch (recentError.type) {
        case RefreshErrorType.network:
          // Network errors: slower progression to allow recovery
          return Duration(seconds: min(currentDelay.inSeconds + 2, 120));

        case RefreshErrorType.serverError:
          // Server errors: moderate progression
          return Duration(seconds: min(currentDelay.inSeconds + 1, 90));

        case RefreshErrorType.authentication:
        case RefreshErrorType.tokenExpired:
          // Auth errors: faster progression since they're likely permanent
          return Duration(seconds: min(currentDelay.inSeconds, 30));

        case RefreshErrorType.unknown:
          // Unknown errors: standard exponential backoff
          return Duration(seconds: min(currentDelay.inSeconds * 2, 60));
      }
    }

    // Default exponential backoff
    return Duration(seconds: min(currentDelay.inSeconds * 2, 60));
  }

  /// Detect if we should abort retries early based on error patterns
  bool _shouldAbortRetries(List<RefreshError> errors, int currentRetryCount) {
    if (errors.isEmpty) return false;

    // If we have multiple consecutive auth errors, abort early
    if (errors.length >= 2) {
      final lastTwoErrors = errors.skip(errors.length - 2).toList();
      if (lastTwoErrors.every(
        (e) =>
            e.type == RefreshErrorType.authentication ||
            e.type == RefreshErrorType.tokenExpired,
      )) {
        _authLogger.addLogEntry(
          'Multiple consecutive auth errors detected, aborting retries early',
          importance: AuthLogImportance.important,
        );
        return true;
      }
    }

    // If we have the same error type for all attempts, and it's not network/server, abort
    if (errors.length >= 2) {
      final errorTypes = errors.map((e) => e.type).toSet();
      if (errorTypes.length == 1) {
        final singleErrorType = errorTypes.first;
        if (singleErrorType == RefreshErrorType.authentication ||
            singleErrorType == RefreshErrorType.tokenExpired) {
          _authLogger.addLogEntry(
            'Consistent ${singleErrorType.name} errors detected, aborting retries early',
            importance: AuthLogImportance.important,
          );
          return true;
        }
      }
    }

    return false;
  }

  /// Optimize retry count based on error patterns
  int _getOptimizedMaxRetries(List<RefreshError> errors) {
    if (errors.isEmpty) return _maxRefreshRetries;

    // For auth errors, reduce max retries
    final hasAuthErrors = errors.any(
      (e) =>
          e.type == RefreshErrorType.authentication ||
          e.type == RefreshErrorType.tokenExpired,
    );

    if (hasAuthErrors) {
      return min(_maxRefreshRetries, 2); // Max 2 retries for auth errors
    }

    // For network errors, allow more retries
    final hasNetworkErrors = errors.any(
      (e) => e.type == RefreshErrorType.network,
    );
    if (hasNetworkErrors) {
      return min(
        _maxRefreshRetries + 1,
        5,
      ); // Allow 1 extra retry for network errors
    }

    return _maxRefreshRetries;
  }

  /// Enhanced session refresh with comprehensive error handling and fallback mechanisms
  Future<Session?> _refreshSessionWithEnhancedErrorHandling() async {
    int retryCount = 0;
    Session? refreshedSession;
    Duration retryDelay = _initialRetryDelay;
    final List<RefreshError> refreshErrors = [];
    bool networkIssueDetected = false;
    bool authIssueDetected = false;

    // Get the current session before attempting refresh
    final currentSession = _repository.getCurrentSession();
    if (currentSession == null) {
      _authLogger.addLogEntry(
        'No current session available before refresh attempt',
        importance: AuthLogImportance.important,
      );
    } else {
      _authLogger.addLogEntry(
        'Current session before refresh: expires in ${_getTimeUntilExpiry(currentSession).inMinutes} minutes',
        importance: AuthLogImportance.verbose,
      );
    }

    while (retryCount < _maxRefreshRetries) {
      try {
        _authLogger.addLogEntry(
          'Attempting session refresh (attempt ${retryCount + 1}/$_maxRefreshRetries)',
          importance: AuthLogImportance.verbose,
        );

        refreshedSession = await _repository.refreshSession();

        if (refreshedSession != null) {
          final timeUntilExpiry = _getTimeUntilExpiry(refreshedSession);

          _authLogger.addLogEntry(
            'Session refreshed successfully on attempt ${retryCount + 1}. New expiry: ${timeUntilExpiry.inMinutes} minutes',
            importance: AuthLogImportance.verbose,
          );

          // Verify the refreshed session is actually newer than the current one
          if (currentSession != null &&
              refreshedSession.expiresAt != null &&
              currentSession.expiresAt != null &&
              refreshedSession.expiresAt! <= currentSession.expiresAt!) {
            _authLogger.addLogEntry(
              'Warning: Refreshed session does not have a newer expiry time. This may indicate a refresh issue.',
              importance: AuthLogImportance.important,
            );
          }

          // Update the last refresh time
          await _updateLastRefreshTime();

          return refreshedSession;
        } else {
          _authLogger.addLogEntry(
            'Session refresh returned null on attempt ${retryCount + 1}',
            importance: AuthLogImportance.important,
          );

          // If we still have a valid current session, return it as fallback
          if (currentSession != null &&
              !_getTimeUntilExpiry(currentSession).isNegative) {
            _authLogger.addLogEntry(
              'Using current session as fallback since it is still valid',
              importance: AuthLogImportance.important,
            );
            return currentSession;
          }
        }
      } catch (e) {
        // Categorize the error
        final errorType = _categorizeRefreshError(e);
        final refreshError = RefreshError(
          type: errorType,
          message: e.toString(),
          originalError: e,
          attemptNumber: retryCount + 1,
        );

        refreshErrors.add(refreshError);

        // Update issue detection flags
        if (errorType == RefreshErrorType.network) {
          networkIssueDetected = true;
        } else if (errorType == RefreshErrorType.authentication ||
            errorType == RefreshErrorType.tokenExpired) {
          authIssueDetected = true;
        }

        _authLogger.addLogEntry(
          'Error refreshing session on attempt ${retryCount + 1}: $e (Type: $errorType)',
          importance: AuthLogImportance.critical,
        );

        // Handle different error types with specific strategies
        final shouldRetry = _shouldRetryBasedOnError(
          errorType,
          retryCount,
          currentSession,
        );

        if (!shouldRetry) {
          _authLogger.addLogEntry(
            'Error type $errorType indicates no retry should be attempted',
            importance: AuthLogImportance.important,
          );
          break;
        }

        // If we still have a valid current session, consider using it as fallback
        if (currentSession != null &&
            !_getTimeUntilExpiry(currentSession).isNegative) {
          // For network errors, use current session as fallback if it's still valid for a while
          if (errorType == RefreshErrorType.network &&
              _getTimeUntilExpiry(currentSession) >
                  const Duration(minutes: 5)) {
            _authLogger.addLogEntry(
              'Network error detected, using current session as fallback since it has ${_getTimeUntilExpiry(currentSession).inMinutes} minutes remaining',
              importance: AuthLogImportance.important,
            );
            return currentSession;
          }

          // For auth errors on final attempt, still try to use current session if valid
          if (authIssueDetected && retryCount >= _maxRefreshRetries - 1) {
            _authLogger.addLogEntry(
              'Auth error on final attempt, using current session as last resort',
              importance: AuthLogImportance.important,
            );
            return currentSession;
          }
        }
      }

      // Increment retry count
      retryCount++;

      // Check if we should abort retries early based on error patterns
      if (_shouldAbortRetries(refreshErrors, retryCount)) {
        _authLogger.addLogEntry(
          'Aborting retries early due to error patterns',
          importance: AuthLogImportance.important,
        );
        break;
      }

      // Get optimized max retries based on error patterns
      final optimizedMaxRetries = _getOptimizedMaxRetries(refreshErrors);

      // If we've reached the optimized max retries, break
      if (retryCount >= optimizedMaxRetries) {
        _authLogger.addLogEntry(
          'Optimized max refresh retries ($optimizedMaxRetries) reached, giving up',
          importance: AuthLogImportance.important,
        );
        break;
      }

      // Calculate adaptive retry delay based on error types and attempt number
      final adaptiveDelay = _calculateAdaptiveRetryDelay(
        retryDelay,
        retryCount,
        refreshErrors,
        networkIssueDetected,
        authIssueDetected,
      );

      _authLogger.addLogEntry(
        'Waiting ${adaptiveDelay.inSeconds} seconds before retry ${retryCount + 1} '
        '(adaptive backoff based on error patterns)',
        importance: AuthLogImportance.verbose,
      );

      await Future.delayed(adaptiveDelay);

      // Update retry delay for next iteration
      retryDelay = _updateRetryDelay(adaptiveDelay, refreshErrors);
    }

    // If all refresh attempts failed but we still have a valid current session, return it
    if (currentSession != null &&
        !_getTimeUntilExpiry(currentSession).isNegative) {
      _authLogger.addLogEntry(
        'All refresh attempts failed but current session is still valid, using it as fallback',
        importance: AuthLogImportance.important,
      );
      return currentSession;
    }

    _authLogger.addLogEntry(
      'CRITICAL: All refresh attempts failed and no valid session available',
      importance: AuthLogImportance.critical,
    );

    // Log enhanced error analysis
    _logRefreshFailureAnalysis(
      refreshErrors,
      networkIssueDetected,
      authIssueDetected,
    );

    // Handle the session expiration with enhanced context
    final primaryErrorType = _getPrimaryErrorType(refreshErrors);
    final reason = _buildFailureReason(refreshErrors, primaryErrorType);
    await _handleSessionExpiration(reason: reason);

    return null;
  }

  /// Get the time until a session expires
  Duration _getTimeUntilExpiry(Session session) {
    if (session.expiresAt == null) {
      return const Duration(
        minutes: -1,
      ); // Return negative duration if no expiry
    }

    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();
    return expiresAt.difference(now);
  }

  /// Get the last active time from storage
  Future<DateTime?> _getLastActiveTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastActiveMs = prefs.getInt(_lastAppActiveTimeKey);
      if (lastActiveMs != null) {
        return DateTime.fromMillisecondsSinceEpoch(lastActiveMs);
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error retrieving last active time: $e',
        importance: AuthLogImportance.important,
      );
    }
    return null;
  }

  /// Perform comprehensive session validation on app resume
  Future<ResumeValidationResult> _performResumeSessionValidation() async {
    try {
      // Check if we have a current session
      final currentSession = _repository.getCurrentSession();

      if (currentSession == null) {
        _authLogger.addLogEntry(
          'No active session found during resume validation',
          importance: AuthLogImportance.important,
        );
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.noSession,
          reason: 'No active session found',
        );
      }

      // Check session integrity
      if (!await _verifySessionIntegrity(
        currentSession,
        await SharedPreferences.getInstance(),
      )) {
        _authLogger.addLogEntry(
          'Session integrity check failed during resume validation',
          importance: AuthLogImportance.critical,
        );
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.sessionCorrupted,
          reason: 'Session integrity check failed',
          currentSession: currentSession,
        );
      }

      // Check if session is expired
      final timeUntilExpiry = _getTimeUntilExpiry(currentSession);
      if (timeUntilExpiry.isNegative) {
        _authLogger.addLogEntry(
          'Session is expired during resume validation',
          importance: AuthLogImportance.important,
        );
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.sessionExpired,
          reason: 'Session has expired',
          currentSession: currentSession,
        );
      }

      // Check for extended inactivity
      final lastActiveTime = await _getLastActiveTime();
      Duration? inactivityDuration;
      if (lastActiveTime != null) {
        inactivityDuration = DateTime.now().difference(lastActiveTime);
        if (inactivityDuration > _extendedInactivityThreshold) {
          _authLogger.addLogEntry(
            'Extended inactivity detected: ${inactivityDuration.inMinutes} minutes',
            importance: AuthLogImportance.important,
          );
          return ResumeValidationResult.invalid(
            failureType: ResumeValidationFailureType.extendedInactivity,
            reason: 'Extended inactivity period detected',
            currentSession: currentSession,
            inactivityDuration: inactivityDuration,
          );
        }
      }

      // Check if session needs proactive refresh
      final dynamicRefreshThreshold = _calculateRefreshThreshold(
        currentSession,
      );
      if (timeUntilExpiry < dynamicRefreshThreshold) {
        _authLogger.addLogEntry(
          'Session needs refresh on resume (${timeUntilExpiry.inMinutes} minutes remaining)',
          importance: AuthLogImportance.verbose,
        );
        // This is not a failure, but indicates refresh is needed
      }

      // Validate session persistence
      try {
        final sessionData = await _validateAndLoadSessionData(
          await SharedPreferences.getInstance(),
        );
        if (sessionData == null) {
          _authLogger.addLogEntry(
            'Session persistence validation failed during resume',
            importance: AuthLogImportance.important,
          );
          return ResumeValidationResult.invalid(
            failureType: ResumeValidationFailureType.persistenceFailure,
            reason: 'Session persistence validation failed',
            currentSession: currentSession,
          );
        }
      } catch (e) {
        _authLogger.addLogEntry(
          'Error validating session persistence: $e',
          importance: AuthLogImportance.important,
        );
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.persistenceFailure,
          reason: 'Session persistence error: $e',
          currentSession: currentSession,
        );
      }

      return ResumeValidationResult.valid(currentSession);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during resume session validation: $e',
        importance: AuthLogImportance.critical,
      );
      return ResumeValidationResult.invalid(
        failureType: ResumeValidationFailureType.networkIssue,
        reason: 'Validation error: $e',
      );
    }
  }

  /// Handle resume validation failure with appropriate recovery strategies
  Future<void> _handleResumeValidationFailure(
    ResumeValidationResult result,
  ) async {
    switch (result.failureType!) {
      case ResumeValidationFailureType.noSession:
        _authLogger.addLogEntry(
          'Handling no session failure - attempting session restoration',
          importance: AuthLogImportance.important,
        );
        await _loadPersistedSession();
        break;

      case ResumeValidationFailureType.sessionExpired:
        _authLogger.addLogEntry(
          'Handling expired session - attempting refresh',
          importance: AuthLogImportance.important,
        );
        await _checkAndRefreshSession();
        break;

      case ResumeValidationFailureType.sessionCorrupted:
        _authLogger.addLogEntry(
          'Handling corrupted session - attempting recovery',
          importance: AuthLogImportance.critical,
        );
        await _recoverFromCorruptedSession(
          'Resume validation detected corruption',
        );
        break;

      case ResumeValidationFailureType.extendedInactivity:
        _authLogger.addLogEntry(
          'Handling extended inactivity - forcing session refresh',
          importance: AuthLogImportance.important,
        );
        // Force immediate session check and refresh
        await _checkAndRefreshSession();
        break;

      case ResumeValidationFailureType.persistenceFailure:
        _authLogger.addLogEntry(
          'Handling persistence failure - re-persisting current session',
          importance: AuthLogImportance.important,
        );
        if (result.currentSession != null) {
          await _persistSession(result.currentSession!);
        }
        break;

      case ResumeValidationFailureType.networkIssue:
        _authLogger.addLogEntry(
          'Handling network issue - deferring validation',
          importance: AuthLogImportance.important,
        );
        // For network issues, we'll retry validation later
        break;
    }
  }

  /// Handle app resume event
  ///
  /// This method should be called when the app is resumed from the background
  /// to check and refresh the session if needed. It also implements an enhanced
  /// check for extended periods of inactivity.
  Future<void> onAppResumed() async {
    _authLogger.logAppLifecycleEvent('resumed');
    _authLogger.addLogEntry(
      'App resumed, performing enhanced session validation',
      importance: AuthLogImportance.important,
    );

    // Ensure Supabase is initialized
    if (!_repository.isSupabaseInitialized()) {
      _authLogger.addLogEntry(
        'Supabase not initialized on resume, initializing now',
        importance: AuthLogImportance.important,
      );
      await _repository.initialize();
    }

    // Update app state and reset background tracking
    _isAppActive = true;
    _resetBackgroundTracking();
    await _updateLastActiveTime();

    // Perform comprehensive session validation on resume
    final resumeValidationResult = await _performResumeSessionValidation();

    if (!resumeValidationResult.isValid) {
      _authLogger.addLogEntry(
        'Resume session validation failed: ${resumeValidationResult.reason}',
        importance: AuthLogImportance.critical,
      );

      // Attempt recovery based on validation failure type
      await _handleResumeValidationFailure(resumeValidationResult);
    } else {
      _authLogger.addLogEntry(
        'Resume session validation passed',
        importance: AuthLogImportance.verbose,
      );
    }

    // Always perform session check and refresh after validation/recovery
    await _checkAndRefreshSession();

    // Restart the refresh timer to ensure it's running with the correct interval
    _startRefreshTimer();

    _authLogger.addLogEntry(
      'App resume session check complete',
      importance: AuthLogImportance.verbose,
    );
  }

  /// Prepare session for background operation
  /// This method optimizes session state for background operation
  Future<void> _prepareSessionForBackground() async {
    _authLogger.addLogEntry(
      'Preparing session for background operation',
      importance: AuthLogImportance.verbose,
    );

    try {
      final currentSession = _repository.getCurrentSession();

      if (currentSession == null) {
        _authLogger.addLogEntry(
          'No session to prepare for background',
          importance: AuthLogImportance.verbose,
        );
        return;
      }

      // Check if session needs immediate refresh before going to background
      final timeUntilExpiry = _getTimeUntilExpiry(currentSession);
      final backgroundRefreshThreshold = _calculateBackgroundRefreshThreshold(
        currentSession,
      );

      if (timeUntilExpiry < backgroundRefreshThreshold) {
        _authLogger.addLogEntry(
          'Session expires soon (${timeUntilExpiry.inMinutes} minutes), refreshing before background',
          importance: AuthLogImportance.important,
        );

        // Perform immediate refresh to ensure session survives background period
        await _checkAndRefreshSession();
      } else {
        _authLogger.addLogEntry(
          'Session has sufficient time (${timeUntilExpiry.inMinutes} minutes) for background operation',
          importance: AuthLogImportance.verbose,
        );
      }

      // Ensure session is properly persisted before background
      await _persistSession(currentSession);

      // Perform background session health check
      await _performBackgroundSessionHealthCheck();
    } catch (e) {
      _authLogger.addLogEntry(
        'Error preparing session for background: $e',
        importance: AuthLogImportance.important,
      );
    }
  }

  /// Calculate refresh threshold specifically for background operation
  Duration _calculateBackgroundRefreshThreshold(Session session) {
    // Use a more conservative threshold for background - refresh if less than 30 minutes remaining
    const backgroundSafetyBuffer = Duration(minutes: 30);

    // But also consider the normal dynamic threshold
    final normalThreshold = _calculateRefreshThreshold(session);

    // Use the larger of the two to be more conservative
    return backgroundSafetyBuffer > normalThreshold
        ? backgroundSafetyBuffer
        : normalThreshold;
  }

  /// Perform background session health check
  Future<void> _performBackgroundSessionHealthCheck() async {
    try {
      final currentSession = _repository.getCurrentSession();
      if (currentSession == null) return;

      // Verify session integrity before background
      final prefs = await SharedPreferences.getInstance();
      if (!await _verifySessionIntegrity(currentSession, prefs)) {
        _authLogger.addLogEntry(
          'Session integrity check failed before background, attempting repair',
          importance: AuthLogImportance.important,
        );

        // Try to repair by re-persisting
        await _persistSession(currentSession);
      }

      // Log session status for background monitoring
      _authLogger.addLogEntry(
        'Background session health check completed - Session expires in ${_getTimeUntilExpiry(currentSession).inMinutes} minutes',
        importance: AuthLogImportance.verbose,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during background session health check: $e',
        importance: AuthLogImportance.important,
      );
    }
  }

  /// Handle app paused event
  ///
  /// This method should be called when the app is paused (goes to background)
  /// to update app state and adjust refresh intervals.
  Future<void> onAppPaused() async {
    _authLogger.logAppLifecycleEvent('paused');

    // Update app state and start background tracking
    _isAppActive = false;
    _backgroundStartTime = DateTime.now();
    _backgroundRefreshCount = 0;
    await _updateLastActiveTime();

    // Perform enhanced background session preparation
    await _prepareSessionForBackground();

    // Restart timer with optimized background interval
    _startRefreshTimer();
  }

  /// Handle app inactive event
  ///
  /// This method should be called when the app becomes inactive
  /// to update app state for tracking purposes.
  Future<void> onAppInactive() async {
    _authLogger.logAppLifecycleEvent('inactive');

    // Update app state
    _isAppActive = false;
    await _updateLastActiveTime();
  }

  /// Perform foreground-specific session validation
  /// This is lighter than resume validation but still comprehensive
  Future<ResumeValidationResult> _performForegroundSessionValidation() async {
    try {
      // Check if we have a current session
      final currentSession = _repository.getCurrentSession();

      if (currentSession == null) {
        _authLogger.addLogEntry(
          'No active session found during foreground validation',
          importance: AuthLogImportance.important,
        );
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.noSession,
          reason: 'No active session found on foreground transition',
        );
      }

      // Quick session expiry check
      final timeUntilExpiry = _getTimeUntilExpiry(currentSession);
      if (timeUntilExpiry.isNegative) {
        _authLogger.addLogEntry(
          'Session is expired during foreground validation',
          importance: AuthLogImportance.important,
        );
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.sessionExpired,
          reason: 'Session has expired during foreground transition',
          currentSession: currentSession,
        );
      }

      // Check for moderate inactivity (shorter threshold than resume)
      final lastActiveTime = await _getLastActiveTime();
      if (lastActiveTime != null) {
        final inactivityDuration = DateTime.now().difference(lastActiveTime);
        // Use a shorter threshold for foreground transitions (half of extended threshold)
        final foregroundInactivityThreshold = Duration(
          minutes: _extendedInactivityThreshold.inMinutes ~/ 2,
        );

        if (inactivityDuration > foregroundInactivityThreshold) {
          _authLogger.addLogEntry(
            'Moderate inactivity detected on foreground: ${inactivityDuration.inMinutes} minutes',
            importance: AuthLogImportance.verbose,
          );
          return ResumeValidationResult.invalid(
            failureType: ResumeValidationFailureType.extendedInactivity,
            reason:
                'Moderate inactivity period detected on foreground transition',
            currentSession: currentSession,
            inactivityDuration: inactivityDuration,
          );
        }
      }

      // Light integrity check - just verify basic session structure
      if (currentSession.accessToken.isEmpty ||
          currentSession.user.id.isEmpty) {
        _authLogger.addLogEntry(
          'Session structure validation failed during foreground check',
          importance: AuthLogImportance.important,
        );
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.sessionCorrupted,
          reason: 'Session structure validation failed',
          currentSession: currentSession,
        );
      }

      // Check if session needs refresh soon (more aggressive than normal)
      final dynamicRefreshThreshold = _calculateRefreshThreshold(
        currentSession,
      );
      // Use 1.5x the normal threshold for foreground transitions
      final foregroundRefreshThreshold = Duration(
        milliseconds: (dynamicRefreshThreshold.inMilliseconds * 1.5).round(),
      );

      if (timeUntilExpiry < foregroundRefreshThreshold) {
        _authLogger.addLogEntry(
          'Session will need refresh soon on foreground (${timeUntilExpiry.inMinutes} minutes remaining)',
          importance: AuthLogImportance.verbose,
        );
        // This is not a failure, but indicates proactive refresh is beneficial
      }

      return ResumeValidationResult.valid(currentSession);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during foreground session validation: $e',
        importance: AuthLogImportance.important,
      );
      return ResumeValidationResult.invalid(
        failureType: ResumeValidationFailureType.networkIssue,
        reason: 'Foreground validation error: $e',
      );
    }
  }

  /// Handle foreground validation failure with lighter recovery strategies
  Future<void> _handleForegroundValidationFailure(
    ResumeValidationResult result,
  ) async {
    switch (result.failureType!) {
      case ResumeValidationFailureType.noSession:
        _authLogger.addLogEntry(
          'Handling no session on foreground - quick session restoration',
          importance: AuthLogImportance.important,
        );
        // Try quick session restoration first
        await _loadPersistedSession();
        break;

      case ResumeValidationFailureType.sessionExpired:
        _authLogger.addLogEntry(
          'Handling expired session on foreground - immediate refresh',
          importance: AuthLogImportance.important,
        );
        // Immediate refresh attempt
        await _checkAndRefreshSession();
        break;

      case ResumeValidationFailureType.sessionCorrupted:
        _authLogger.addLogEntry(
          'Handling corrupted session on foreground - light recovery',
          importance: AuthLogImportance.important,
        );
        // Try to re-persist current session if it exists
        if (result.currentSession != null) {
          await _persistSession(result.currentSession!);
        } else {
          await _loadPersistedSession();
        }
        break;

      case ResumeValidationFailureType.extendedInactivity:
        _authLogger.addLogEntry(
          'Handling moderate inactivity on foreground - proactive refresh',
          importance: AuthLogImportance.verbose,
        );
        // Proactive refresh for moderate inactivity
        await _checkAndRefreshSession();
        break;

      case ResumeValidationFailureType.persistenceFailure:
        _authLogger.addLogEntry(
          'Handling persistence failure on foreground - re-persist session',
          importance: AuthLogImportance.verbose,
        );
        if (result.currentSession != null) {
          await _persistSession(result.currentSession!);
        }
        break;

      case ResumeValidationFailureType.networkIssue:
        _authLogger.addLogEntry(
          'Handling network issue on foreground - defer validation',
          importance: AuthLogImportance.verbose,
        );
        // For network issues during foreground transition, just log and continue
        break;
    }
  }

  /// Handle app foreground transition
  ///
  /// This method should be called when the app transitions from background
  /// to foreground state, providing enhanced session validation for this
  /// specific transition type.
  Future<void> onAppForeground() async {
    _authLogger.logAppLifecycleEvent('foreground');
    _authLogger.addLogEntry(
      'App transitioned to foreground, performing session validation',
      importance: AuthLogImportance.important,
    );

    // Update app state and reset background tracking
    _isAppActive = true;
    _resetBackgroundTracking();
    await _updateLastActiveTime();

    // Perform foreground-specific session validation
    final foregroundValidationResult =
        await _performForegroundSessionValidation();

    if (!foregroundValidationResult.isValid) {
      _authLogger.addLogEntry(
        'Foreground session validation failed: ${foregroundValidationResult.reason}',
        importance: AuthLogImportance.critical,
      );

      // Handle validation failure with foreground-specific recovery
      await _handleForegroundValidationFailure(foregroundValidationResult);
    } else {
      _authLogger.addLogEntry(
        'Foreground session validation passed',
        importance: AuthLogImportance.verbose,
      );
    }

    // Perform session refresh if needed
    await _checkAndRefreshSession();

    // Restart timer with active app interval
    _startRefreshTimer();

    _authLogger.addLogEntry(
      'Foreground transition session validation complete',
      importance: AuthLogImportance.verbose,
    );
  }

  /// Handle app detached event
  ///
  /// This method should be called when the app is detached
  /// to log the event for diagnostic purposes.
  void onAppDetached() {
    _authLogger.logAppLifecycleEvent('detached');
  }

  /// Handle session expiration with detailed logging
  ///
  /// This method provides detailed logging about why a session expired
  /// to help diagnose authentication issues.
  Future<void> _handleSessionExpiration({String? reason}) async {
    _authLogger.addLogEntry(
      'SESSION EXPIRED: ${reason ?? "Unknown reason"}',
      importance: AuthLogImportance.critical,
    );

    // Log stack trace to help identify where the expiration was triggered
    _authLogger.addLogEntry(
      'Session expiration stack trace: ${StackTrace.current}',
      importance: AuthLogImportance.critical,
    );

    // Log additional details about the session
    final currentSession = _repository.getCurrentSession();
    if (currentSession != null) {
      _authLogger.addLogEntry(
        'Session that expired was for user: ${currentSession.user.email}',
        importance: AuthLogImportance.critical,
      );

      if (currentSession.expiresAt != null) {
        final expiryTime = DateTime.fromMillisecondsSinceEpoch(
          currentSession.expiresAt! * 1000,
        );
        final now = DateTime.now();
        final timeDiff = now.difference(expiryTime);

        if (timeDiff.isNegative) {
          _authLogger.addLogEntry(
            'Session was set to expire at: ${expiryTime.toLocal()} (in ${-timeDiff.inMinutes} minutes)',
            importance: AuthLogImportance.critical,
          );
          _authLogger.addLogEntry(
            'WARNING: Session expired before its scheduled expiry time',
            importance: AuthLogImportance.critical,
          );
        } else {
          _authLogger.addLogEntry(
            'Session expired at: ${expiryTime.toLocal()} (${timeDiff.inMinutes} minutes ago)',
            importance: AuthLogImportance.critical,
          );
        }
      }

      // Check if refresh token was present
      if (currentSession.refreshToken == null ||
          currentSession.refreshToken!.isEmpty) {
        _authLogger.addLogEntry(
          'No refresh token was available for the expired session',
          importance: AuthLogImportance.critical,
        );
      } else {
        _authLogger.addLogEntry(
          'Refresh token was present but could not be used to refresh the session',
          importance: AuthLogImportance.critical,
        );
      }
    } else {
      _authLogger.addLogEntry(
        'No session object available at time of expiration',
        importance: AuthLogImportance.critical,
      );
    }

    // Clear persisted session data
    await _clearPersistedSession();

    // Update session status
    _updateSessionStatus(SessionStatus.expired);
  }

  /// Handle sign out
  ///
  /// This method should be called when the user signs out
  /// to clear persisted session data.
  Future<void> onSignOut() async {
    _authLogger.addLogEntry(
      'User signed out, clearing persisted session',
      importance: AuthLogImportance.important,
    );
    await _clearPersistedSession();
    _updateSessionStatus(SessionStatus.expired);
  }

  /// Dispose resources
  ///
  /// This method cleans up resources used by the session manager.
  void dispose() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    _authLogger.addLogEntry(
      'Session manager disposed',
      importance: AuthLogImportance.verbose,
    );
  }
}
