import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

import '../repositories/app_settings_repository.dart';
import '../repositories/income_repository.dart';
import '../repositories/level_settings_repository.dart';
import '../repositories/orders_repository.dart';
import '../repositories/performance_repository.dart';
import '../repositories/spare_parts_history_repository.dart';
import '../repositories/spare_parts_repository.dart';
import '../version/database_version_manager.dart';
import 'triggers.dart';

part 'app_database.g.dart';

// Converter to ensure DateTime values are stored in UTC
class UtcDateTimeConverter extends TypeConverter<DateTime, DateTime> {
  const UtcDateTimeConverter();

  @override
  DateTime fromSql(DateTime dateTime) {
    return dateTime.toUtc();
  }

  @override
  DateTime toSql(DateTime value) {
    return value.toUtc();
  }
}

// Enum for sync status
enum SyncStatus { pendingUpload, synced, conflict }

// Converter for SyncStatus enum to string for storage
class SyncStatusConverter extends TypeConverter<SyncStatus, String> {
  const SyncStatusConverter();

  @override
  SyncStatus fromSql(String sqlValue) {
    return SyncStatus.values.firstWhere(
      (status) => status.toString().split('.').last == sqlValue,
      orElse: () => SyncStatus.pendingUpload,
    );
  }

  @override
  String toSql(SyncStatus value) {
    // Extract enum value name from enum.toString() which returns "SyncStatus.value"
    return value.toString().split('.').last;
  }
}

// Income table
class Income extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime().map(const UtcDateTimeConverter())();
  IntColumn get initialMileage => integer()();
  IntColumn get finalMileage => integer()();
  RealColumn get initialGopay => real()();
  RealColumn get initialBca => real()();
  RealColumn get initialCash => real()();
  RealColumn get initialOvo => real()();
  RealColumn get initialBri => real()();
  RealColumn get initialRekpon => real()();
  RealColumn get finalGopay => real()();
  RealColumn get finalBca => real()();
  RealColumn get finalCash => real()();
  RealColumn get finalOvo => real()();
  RealColumn get finalBri => real()();
  RealColumn get finalRekpon => real()();
  RealColumn get initialCapital => real().nullable()(); // Computed field
  RealColumn get finalResult => real().nullable()(); // Computed field
  IntColumn get mileage => integer().nullable()(); // Computed field
  RealColumn get netIncome => real().nullable()(); // Computed field

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

// Orders table
class Orders extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime().map(const UtcDateTimeConverter())();
  IntColumn get orderCompleted => integer()();
  IntColumn get orderMissed => integer()();
  IntColumn get orderCanceled => integer()();
  IntColumn get cbsOrder => integer()();
  IntColumn get incomingOrder => integer().nullable()(); // Computed field
  IntColumn get orderReceived => integer().nullable()(); // Computed field
  RealColumn get bidAcceptance => real().nullable()(); // Computed field
  RealColumn get tripCompletion => real().nullable()(); // Computed field
  IntColumn get points => integer()();
  RealColumn get trip => real()();
  RealColumn get bonus => real()();
  RealColumn get tips => real()();
  RealColumn get income => real().nullable()(); // Computed field

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

// Performance table
class Performance extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime().map(const UtcDateTimeConverter())();
  RealColumn get bidPerformance => real()();
  RealColumn get tripPerformance => real()();
  IntColumn get activeDays => integer()();
  RealColumn get onlineHours => real()();
  RealColumn get avgCompleted => real().nullable()(); // Computed field
  RealColumn get avgOnline => real().nullable()(); // Computed field
  RealColumn get retention => real().nullable()(); // Computed field

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

// Level Settings table
class LevelSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get platinumPointsReq => integer()();
  RealColumn get platinumBidReq => real()();
  RealColumn get platinumTripReq => real()();
  IntColumn get goldPointsReq => integer()();
  RealColumn get goldBidReq => real()();
  RealColumn get goldTripReq => real()();
  IntColumn get silverPointsReq => integer()();
  RealColumn get silverBidReq => real()();
  RealColumn get silverTripReq => real()();
}

// Spare Parts table
class SpareParts extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  TextColumn get partName => text()();
  TextColumn get partType => text()();
  RealColumn get price => real()();
  IntColumn get mileageLimit => integer()();
  IntColumn get initialMileage => integer()();
  DateTimeColumn get installationDate =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  IntColumn get currentMileage => integer().withDefault(const Constant(0))();
  BoolColumn get warningStatus =>
      boolean().withDefault(const Constant(false))();
  // New columns
  IntColumn get replacementCount => integer().withDefault(const Constant(0))();
  TextColumn get notes => text().withDefault(const Constant(''))();

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

// Spare Parts History table
class SparePartsHistory extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  TextColumn get partName => text()();
  TextColumn get partType => text()();
  RealColumn get price => real()();
  DateTimeColumn get replacementDate =>
      dateTime().map(const UtcDateTimeConverter())();
  IntColumn get mileageAtReplacement => integer()();
  IntColumn get sparePartId => integer().references(SpareParts, #id)();
  // New columns for better history tracking
  DateTimeColumn get installationDate => dateTime().map(
    const UtcDateTimeConverter(),
  )(); // When the part was installed
  IntColumn get initialMileage =>
      integer()(); // Mileage when the part was installed
  TextColumn get replacementReason =>
      text().withDefault(const Constant('Regular maintenance'))();
  IntColumn get replacedByPartId =>
      integer().nullable()(); // ID of the new part that replaced this one
  IntColumn get replacementCount =>
      integer().withDefault(const Constant(1))(); // Which replacement this was
  IntColumn get usageDays =>
      integer().withDefault(const Constant(0))(); // Days the part was used
  IntColumn get usageMileage => integer().withDefault(
    const Constant(0),
  )(); // Mileage the part was used for
  TextColumn get notes => text().withDefault(const Constant(''))();

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

// App Settings table
class AppSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  // Date range settings
  DateTimeColumn get dateRangeStart =>
      dateTime().map(const UtcDateTimeConverter())();
  DateTimeColumn get dateRangeEnd =>
      dateTime().map(const UtcDateTimeConverter())();
  // Backup settings
  TextColumn get backupDirectoryPath => text().nullable()();
  // Add other app settings here as needed
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  // Add new field for last sync time
  DateTimeColumn get lastSyncTime =>
      dateTime().nullable().map(const UtcDateTimeConverter())();
}

// Database class with repository pattern implementation
@DriftDatabase(
  tables: [
    Income,
    Orders,
    Performance,
    LevelSettings,
    SpareParts,
    SparePartsHistory,
    AppSettings,
  ],
)
class AppDatabase extends _$AppDatabase {
  // Repository instances
  late final incomeRepo = IncomeRepository(this);
  late final ordersRepo = OrdersRepository(this);
  late final performanceRepo = PerformanceRepository(this);
  late final sparePartsRepo = SparePartsRepository(this);
  late final sparePartsHistoryRepo = SparePartsHistoryRepository(this);
  late final appSettingsRepo = AppSettingsRepository(this);
  late final levelSettingsRepo = LevelSettingsRepository(this);

  AppDatabase(String path) : super(_openConnection(path));

  @override
  int get schemaVersion => DatabaseVersionManager.getCurrentSchemaVersion();

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        // Create tables
        await m.createAll();

        // Create triggers
        await Triggers.createTriggers(executor);
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from == 1 || from == 2) {
          // For SQLite, we need to use a different approach for adding NOT NULL columns
          // First, add nullable columns
          try {
            await customStatement('ALTER TABLE income ADD COLUMN uuid TEXT');
            await customStatement(
              'ALTER TABLE income ADD COLUMN created_at TEXT',
            );
            await customStatement(
              'ALTER TABLE income ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE income ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE income ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in income table: $e');
          }

          try {
            await customStatement('ALTER TABLE orders ADD COLUMN uuid TEXT');
            await customStatement(
              'ALTER TABLE orders ADD COLUMN created_at TEXT',
            );
            await customStatement(
              'ALTER TABLE orders ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE orders ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE orders ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in orders table: $e');
          }

          try {
            await customStatement(
              'ALTER TABLE performance ADD COLUMN uuid TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN created_at TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in performance table: $e');
          }

          try {
            await customStatement(
              'ALTER TABLE spare_parts ADD COLUMN uuid TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in spare_parts table: $e');
          }

          try {
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN uuid TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint(
              'Columns might already exist in spare_parts_history table: $e',
            );
          }

          try {
            await m.addColumn(appSettings, appSettings.lastSyncTime);
          } catch (e) {
            debugPrint(
              'lastSyncTime column might already exist in appSettings table: $e',
            );
          }

          // Generate unique UUIDs for existing records and repair duplicate UUIDs
          final now = DateTime.now().toUtc().toIso8601String();

          // Fix income records
          await _fixTableUuids('income', [
            'created_at',
            'updated_at',
            'sync_status',
          ], now);

          // Fix orders records
          await _fixTableUuids('orders', [
            'created_at',
            'updated_at',
            'sync_status',
          ], now);

          // Fix performance records
          await _fixTableUuids('performance', [
            'created_at',
            'updated_at',
            'sync_status',
          ], now);

          // Fix spare_parts records (no created_at/updated_at columns)
          await _fixTableUuids('spare_parts', ['sync_status'], now);

          // Fix spare_parts_history records
          await _fixTableUuids('spare_parts_history', [
            'updated_at',
            'sync_status',
          ], now);

          // Create triggers
          await Triggers.createTriggers(executor);
        }
      },
      beforeOpen: (details) async {
        // Initialize with default settings if needed
        if (details.wasCreated) {
          await _initializeDefaultSettings();
        }

        // Check database compatibility
        if (!details.wasCreated &&
            details.hadUpgrade &&
            details.versionBefore != null) {
          await _checkDatabaseCompatibility(
            details.versionBefore!,
            details.versionNow,
          );
        }
      },
    );
  }

  Future<void> _initializeDefaultSettings() async {
    // Initialize default level settings using the repository
    await levelSettingsRepo.initializeDefaultSettings();

    // Initialize default app settings
    final now = DateTime.now().toUtc();
    final firstDayOfMonth = DateTime.utc(now.year, now.month, 1);
    final lastDayOfMonth = DateTime.utc(now.year, now.month + 1, 0);

    await appSettingsRepo.insertAppSettings(
      AppSettingsCompanion.insert(
        dateRangeStart: firstDayOfMonth,
        dateRangeEnd: lastDayOfMonth,
      ),
    );
  }

  /// Checks if the current app version is compatible with the database schema
  ///
  /// This method is called when the database is upgraded to ensure that
  /// the app can work with the new schema version.
  Future<void> _checkDatabaseCompatibility(
    int oldVersion,
    int newVersion,
  ) async {
    debugPrint('Database upgraded from schema $oldVersion to $newVersion');

    // Check if the current app version is compatible with the new schema
    if (!DatabaseVersionManager.isCompatibleWithSchema(newVersion)) {
      final minAppVersion = DatabaseVersionManager.getMinAppVersionForSchema(
        newVersion,
      );
      debugPrint(
        'WARNING: Current app version is not compatible with database schema $newVersion',
      );
      debugPrint('Minimum required app version: $minAppVersion');

      // Log this issue - in a real app, you might want to show a user-friendly message
      // or even prevent the app from continuing if the database is incompatible
    }
  }

  // Delegate methods to repositories for backward compatibility

  // Income-related methods
  Future<List<IncomeData>> getAllIncome() => incomeRepo.getAll();
  Future<int> insertIncome(IncomeCompanion entity) => incomeRepo.insert(entity);
  Future<bool> updateIncome(IncomeData entity) => incomeRepo.update(entity);
  Future<int> deleteIncome(int id) => incomeRepo.delete(id);
  Future<bool> updateIncomeWithSync(IncomeData entity) =>
      incomeRepo.updateWithSync(entity);
  Future<bool> softDeleteIncome(int id) => incomeRepo.softDelete(id);

  // Orders-related methods
  Future<List<Order>> getAllOrders() => ordersRepo.getAll();
  Future<int> insertOrder(OrdersCompanion entity) => ordersRepo.insert(entity);
  Future<bool> updateOrder(Order entity) => ordersRepo.update(entity);
  Future<bool> updateOrderWithSync(OrdersCompanion entity) =>
      ordersRepo.updateWithSync(entity);
  Future<int> deleteOrder(int id) => ordersRepo.delete(id);
  Future<bool> softDeleteOrder(int id) => ordersRepo.softDelete(id);

  // Performance-related methods
  Future<List<PerformanceData>> getAllPerformance() => performanceRepo.getAll();
  Future<int> insertPerformance(PerformanceCompanion entity) =>
      performanceRepo.insert(entity);
  Future<bool> updatePerformance(PerformanceData entity) =>
      performanceRepo.update(entity);
  Future<bool> updatePerformanceWithSync(PerformanceData entity) =>
      performanceRepo.updateWithSync(entity);
  Future<int> deletePerformance(int id) => performanceRepo.delete(id);
  Future<bool> softDeletePerformance(int id) => performanceRepo.softDelete(id);

  // Level settings methods
  Future<LevelSetting?> getLevelSettings() =>
      levelSettingsRepo.getLevelSettings();
  Future<bool> updateLevelSettings(LevelSettingsCompanion entity) =>
      levelSettingsRepo.updateLevelSettings(entity);

  // Spare parts methods
  Future<List<SparePart>> getAllSpareParts() => sparePartsRepo.getAll();
  Future<int> insertSparePart(SparePartsCompanion entity) =>
      sparePartsRepo.insert(entity);
  Future<bool> updateSparePart(SparePart entity) =>
      sparePartsRepo.update(entity);
  Future<bool> updateSparePartWithSync(SparePart entity) =>
      sparePartsRepo.updateWithSync(entity);
  Future<int> deleteSparePart(int id) => sparePartsRepo.delete(id);
  Future<bool> softDeleteSparePart(int id) => sparePartsRepo.softDelete(id);
  Future<void> updateSparePartsMileage(int latestMileage) =>
      sparePartsRepo.updateSparePartsMileage(latestMileage);

  // Spare parts history methods
  Future<List<SparePartsHistoryData>> getHistoryForSparePart(int sparePartId) =>
      sparePartsHistoryRepo.getHistoryForSparePart(sparePartId);
  Future<int> insertSparePartHistory(SparePartsHistoryCompanion entity) =>
      sparePartsHistoryRepo.insert(entity);
  Future<bool> updateSparePartHistoryWithSync(SparePartsHistoryData entity) =>
      sparePartsHistoryRepo.updateWithSync(entity);
  Future<bool> softDeleteSparePartHistory(int id) =>
      sparePartsHistoryRepo.softDelete(id);

  // App Settings methods
  Future<AppSetting?> getAppSettings() => appSettingsRepo.getAppSettings();
  Future<int> insertAppSettings(AppSettingsCompanion entity) =>
      appSettingsRepo.insertAppSettings(entity);
  Future<bool> updateAppSettings(AppSettingsCompanion entity) =>
      appSettingsRepo.updateAppSettings(entity);
  Future<AppSetting> getOrCreateAppSettings() =>
      appSettingsRepo.getOrCreateAppSettings();
  Future<void> updateLastSyncTime(DateTime time) =>
      appSettingsRepo.updateLastSyncTime(time);
  Future<DateTime?> getLastSyncTime() => appSettingsRepo.getLastSyncTime();

  // Get all unsynchronized records (for uploading to cloud)
  Future<List<IncomeData>> getUnsyncedIncome() =>
      incomeRepo.getUnsyncedRecords();
  Future<List<Order>> getUnsyncedOrders() => ordersRepo.getUnsyncedRecords();
  Future<List<PerformanceData>> getUnsyncedPerformance() =>
      performanceRepo.getUnsyncedRecords();
  Future<List<SparePart>> getUnsyncedSpareParts() =>
      sparePartsRepo.getUnsyncedRecords();
  Future<List<SparePartsHistoryData>> getUnsyncedSparePartsHistory() =>
      sparePartsHistoryRepo.getUnsyncedRecords();

  // Mark records as synced
  Future<void> markIncomeAsSynced(String uuid) => incomeRepo.markAsSynced(uuid);
  Future<void> markOrderAsSynced(String uuid) => ordersRepo.markAsSynced(uuid);
  Future<void> markPerformanceAsSynced(String uuid) =>
      performanceRepo.markAsSynced(uuid);
  Future<void> markSparePartAsSynced(String uuid) =>
      sparePartsRepo.markAsSynced(uuid);
  Future<void> markSparePartHistoryAsSynced(String uuid) =>
      sparePartsHistoryRepo.markAsSynced(uuid);

  /// Repair database by fixing UUID conflicts across all tables
  /// This method can be called to fix existing databases that have duplicate UUIDs
  Future<void> repairDatabaseUuids() async {
    debugPrint('Starting database UUID repair process');
    final now = DateTime.now().toUtc().toIso8601String();

    try {
      // Fix all tables that have UUID columns
      await _fixTableUuids('income', [
        'created_at',
        'updated_at',
        'sync_status',
      ], now);
      await _fixTableUuids('orders', [
        'created_at',
        'updated_at',
        'sync_status',
      ], now);
      await _fixTableUuids('performance', [
        'created_at',
        'updated_at',
        'sync_status',
      ], now);
      await _fixTableUuids('spare_parts', ['sync_status'], now);
      await _fixTableUuids('spare_parts_history', [
        'updated_at',
        'sync_status',
      ], now);

      debugPrint('Database UUID repair process completed successfully');
    } catch (e) {
      debugPrint('Error during database UUID repair: $e');
      rethrow;
    }
  }

  /// Validate database integrity by checking for UUID conflicts
  /// Returns a map of table names to lists of issues found
  Future<Map<String, List<String>>> validateDatabaseIntegrity() async {
    final issues = <String, List<String>>{};

    try {
      debugPrint('Starting database integrity validation');

      // Check each table for UUID issues
      final tables = [
        'income',
        'orders',
        'performance',
        'spare_parts',
        'spare_parts_history',
      ];

      for (final tableName in tables) {
        final tableIssues = <String>[];

        // Check for NULL UUIDs
        final nullUuidCount = await customSelect(
          'SELECT COUNT(*) as count FROM $tableName WHERE uuid IS NULL',
        ).getSingle();

        if (nullUuidCount.data['count'] > 0) {
          tableIssues.add(
            '${nullUuidCount.data['count']} records with NULL UUIDs',
          );
        }

        // Check for duplicate UUIDs
        final duplicateUuids = await customSelect('''
          SELECT uuid, COUNT(*) as count
          FROM $tableName
          WHERE uuid IS NOT NULL
          GROUP BY uuid
          HAVING COUNT(*) > 1
        ''').get();

        if (duplicateUuids.isNotEmpty) {
          final totalDuplicates = duplicateUuids.fold<int>(
            0,
            (sum, row) => sum + (row.data['count'] as int) - 1,
          );
          tableIssues.add(
            '${duplicateUuids.length} UUID groups with $totalDuplicates duplicate records',
          );
        }

        if (tableIssues.isNotEmpty) {
          issues[tableName] = tableIssues;
        }
      }

      if (issues.isEmpty) {
        debugPrint('Database integrity validation passed - no issues found');
      } else {
        debugPrint('Database integrity validation found issues: $issues');
      }

      return issues;
    } catch (e) {
      debugPrint('Error during database integrity validation: $e');
      return {
        'validation_error': ['Failed to validate database: $e'],
      };
    }
  }

  /// Fix UUID issues in a table by generating unique UUIDs for records that have NULL or duplicate UUIDs
  Future<void> _fixTableUuids(
    String tableName,
    List<String> additionalColumns,
    String now,
  ) async {
    try {
      debugPrint('Fixing UUIDs for table: $tableName');

      // First, handle records with NULL UUIDs
      final nullUuidCount = await customSelect(
        'SELECT COUNT(*) as count FROM $tableName WHERE uuid IS NULL',
      ).getSingle();

      if (nullUuidCount.data['count'] > 0) {
        debugPrint(
          'Found ${nullUuidCount.data['count']} records with NULL UUIDs in $tableName',
        );

        // Get all records with NULL UUIDs
        final recordsWithNullUuid = await customSelect(
          'SELECT id FROM $tableName WHERE uuid IS NULL',
        ).get();

        // Update each record individually with a unique UUID
        for (final record in recordsWithNullUuid) {
          final newUuid = const Uuid().v4();
          final updateColumns = ['uuid = ?'];
          final updateValues = [newUuid];

          // Add additional columns based on table structure
          for (final column in additionalColumns) {
            if (column == 'created_at' || column == 'updated_at') {
              updateColumns.add('$column = ?');
              updateValues.add(now);
            } else if (column == 'sync_status') {
              updateColumns.add('$column = ?');
              updateValues.add('synced');
            }
          }

          updateValues.add(record.data['id']);

          await customStatement(
            'UPDATE $tableName SET ${updateColumns.join(', ')} WHERE id = ?',
            updateValues,
          );
        }
      }

      // Next, handle duplicate UUIDs by finding groups of records with the same UUID
      final duplicateUuids = await customSelect('''
        SELECT uuid, COUNT(*) as count
        FROM $tableName
        WHERE uuid IS NOT NULL
        GROUP BY uuid
        HAVING COUNT(*) > 1
      ''').get();

      if (duplicateUuids.isNotEmpty) {
        debugPrint(
          'Found ${duplicateUuids.length} duplicate UUID groups in $tableName',
        );

        for (final duplicateGroup in duplicateUuids) {
          final duplicateUuid = duplicateGroup.data['uuid'] as String;
          final count = duplicateGroup.data['count'] as int;

          debugPrint(
            'Fixing $count records with duplicate UUID: $duplicateUuid',
          );

          // Get all records with this duplicate UUID, ordered by ID to keep the first one unchanged
          final duplicateRecords = await customSelect(
            'SELECT id FROM $tableName WHERE uuid = ? ORDER BY id',
            variables: [Variable(duplicateUuid)],
          ).get();

          // Skip the first record (keep its UUID), update the rest with new UUIDs
          for (int i = 1; i < duplicateRecords.length; i++) {
            final record = duplicateRecords[i];
            final newUuid = const Uuid().v4();
            final updateColumns = ['uuid = ?'];
            final updateValues = [newUuid];

            // Add additional columns for sync status update
            for (final column in additionalColumns) {
              if (column == 'updated_at') {
                updateColumns.add('$column = ?');
                updateValues.add(now);
              } else if (column == 'sync_status') {
                updateColumns.add('$column = ?');
                updateValues.add(
                  'pendingUpload',
                ); // Mark as pending since UUID changed
              }
            }

            updateValues.add(record.data['id']);

            await customStatement(
              'UPDATE $tableName SET ${updateColumns.join(', ')} WHERE id = ?',
              updateValues,
            );
          }
        }
      }

      debugPrint('Successfully fixed UUIDs for table: $tableName');
    } catch (e) {
      debugPrint('Error fixing UUIDs for table $tableName: $e');
    }
  }
}

LazyDatabase _openConnection(String path) {
  return LazyDatabase(() async {
    return NativeDatabase(File(path));
  });
}
